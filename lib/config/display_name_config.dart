/// Configuration for display name providers and related constants
/// 
/// This file centralizes all display name provider configurations
/// to avoid repetition throughout the codebase.

/// Available display name providers
enum DisplayNameProvider {
  gf,
  xbox,
  playstation,
  steam,
  discord,
  twitch;

  /// Get the string representation of the provider
  String get value {
    switch (this) {
      case DisplayNameProvider.gf:
        return 'gf';
      case DisplayNameProvider.xbox:
        return 'xbox';
      case DisplayNameProvider.playstation:
        return 'playstation';
      case DisplayNameProvider.steam:
        return 'steam';
      case DisplayNameProvider.discord:
        return 'discord';
      case DisplayNameProvider.twitch:
        return 'twitch';
    }
  }

  /// Get the display name for UI purposes
  String get displayName {
    switch (this) {
      case DisplayNameProvider.gf:
        return 'GameFlex';
      case DisplayNameProvider.xbox:
        return 'Xbox Live';
      case DisplayNameProvider.playstation:
        return 'PlayStation';
      case DisplayNameProvider.steam:
        return 'Steam';
      case DisplayNameProvider.discord:
        return 'Discord';
      case DisplayNameProvider.twitch:
        return 'Twitch';
    }
  }

  /// Get the provider from string value
  static DisplayNameProvider fromString(String value) {
    switch (value.toLowerCase()) {
      case 'gf':
        return DisplayNameProvider.gf;
      case 'xbox':
        return DisplayNameProvider.xbox;
      case 'playstation':
        return DisplayNameProvider.playstation;
      case 'steam':
        return DisplayNameProvider.steam;
      case 'discord':
        return DisplayNameProvider.discord;
      case 'twitch':
        return DisplayNameProvider.twitch;
      default:
        throw ArgumentError('Unknown display name provider: $value');
    }
  }

  /// Check if this provider supports account linking
  bool get supportsLinking {
    switch (this) {
      case DisplayNameProvider.gf:
        return false; // GameFlex is the native platform
      case DisplayNameProvider.xbox:
      case DisplayNameProvider.playstation:
      case DisplayNameProvider.steam:
      case DisplayNameProvider.discord:
      case DisplayNameProvider.twitch:
        return true;
    }
  }

  /// Get the icon data for this provider
  String get iconAsset {
    switch (this) {
      case DisplayNameProvider.gf:
        return 'assets/icons/gameflex_icon.png';
      case DisplayNameProvider.xbox:
        return 'assets/icons/xbox_icon.png';
      case DisplayNameProvider.playstation:
        return 'assets/icons/playstation_icon.png';
      case DisplayNameProvider.steam:
        return 'assets/icons/steam_icon.png';
      case DisplayNameProvider.discord:
        return 'assets/icons/discord_icon.png';
      case DisplayNameProvider.twitch:
        return 'assets/icons/twitch_icon.png';
    }
  }

  /// Get the primary color for this provider
  int get primaryColor {
    switch (this) {
      case DisplayNameProvider.gf:
        return 0xFF00FF88; // GameFlex green
      case DisplayNameProvider.xbox:
        return 0xFF107C10; // Xbox green
      case DisplayNameProvider.playstation:
        return 0xFF0070D1; // PlayStation blue
      case DisplayNameProvider.steam:
        return 0xFF1B2838; // Steam dark blue
      case DisplayNameProvider.discord:
        return 0xFF5865F2; // Discord blurple
      case DisplayNameProvider.twitch:
        return 0xFF9146FF; // Twitch purple
    }
  }
}

/// Display name configuration constants
class DisplayNameConfig {
  /// All available providers as strings (for backend compatibility)
  static const List<String> allProviders = [
    'gf',
    'xbox',
    'playstation',
    'steam',
    'discord',
    'twitch',
  ];

  /// Providers that support account linking
  static const List<String> linkableProviders = [
    'xbox',
    'playstation',
    'steam',
    'discord',
    'twitch',
  ];

  /// Default provider for new accounts
  static const String defaultProvider = 'gf';

  /// Maximum length for display names
  static const int maxDisplayNameLength = 32;

  /// Minimum length for display names
  static const int minDisplayNameLength = 3;

  /// Regex pattern for valid display names
  static const String displayNamePattern = r'^[a-zA-Z0-9_-]+$';

  /// Get provider display name from string
  static String getProviderDisplayName(String provider) {
    try {
      return DisplayNameProvider.fromString(provider).displayName;
    } catch (e) {
      return provider.toUpperCase();
    }
  }

  /// Check if a provider is valid
  static bool isValidProvider(String provider) {
    return allProviders.contains(provider.toLowerCase());
  }

  /// Check if a provider supports linking
  static bool supportsLinking(String provider) {
    return linkableProviders.contains(provider.toLowerCase());
  }
}
