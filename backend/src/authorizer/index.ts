import {
    CognitoIdentityProviderClient,
    GetUserCommand,
    GetUserCommandOutput,
    AttributeType
} from '@aws-sdk/client-cognito-identity-provider';
import {
    DynamoDBClient
} from '@aws-sdk/client-dynamodb';
import {
    DynamoDBDocumentClient,
    QueryCommand
} from '@aws-sdk/lib-dynamodb';
import { APIGatewayRequestAuthorizerEvent } from 'aws-lambda';
import { DisplayNameProvider } from '/opt/nodejs/display-name-config';

// Configure AWS SDK v3 clients
const cognitoClient = new CognitoIdentityProviderClient({
    region: process.env.AWS_REGION || 'us-west-2'
});

const dynamodbClient = new DynamoDBClient({
    region: process.env.AWS_REGION || 'us-west-2'
});
const dynamodb = DynamoDBDocumentClient.from(dynamodbClient);

// Environment variables
const USERS_TABLE = process.env.USERS_TABLE;

// TypeScript interfaces
interface PolicyDocument {
    Version: string;
    Statement: PolicyStatement[];
}

interface PolicyStatement {
    Action: string;
    Effect: 'Allow' | 'Deny';
    Resource: string;
}

interface AuthResponse {
    principalId: string;
    policyDocument?: PolicyDocument;
    context?: Record<string, string>;
}

interface UserAttributes {
    [key: string]: string;
}

interface UserRecord {
    id: string;
    email: string;
    username?: string;
    firstName: string;
    lastName: string;
    displayName?: string;
    displayNameProvider?: DisplayNameProvider;
    cognitoUserId: string;
    createdAt: string;
    updatedAt: string;
}

// Helper function to get DynamoDB user ID from Cognito user ID
const getDynamoUserIdFromCognitoId = async (cognitoUserId: string): Promise<string | null> => {
    if (!USERS_TABLE) {
        console.error('USERS_TABLE environment variable not set');
        return null;
    }

    try {
        const queryCommand = new QueryCommand({
            TableName: USERS_TABLE,
            IndexName: 'CognitoUserIdIndex',
            KeyConditionExpression: 'cognitoUserId = :cognitoUserId',
            ExpressionAttributeValues: {
                ':cognitoUserId': cognitoUserId
            }
        });

        const result = await dynamodb.send(queryCommand);

        if (result.Items && result.Items.length > 0) {
            const user = result.Items[0] as UserRecord;
            return user.id;
        }

        return null;
    } catch (error) {
        console.error('Error looking up user by Cognito ID:', error);
        return null;
    }
};

// Helper function to generate policy
const generatePolicy = (
    principalId: string,
    effect: 'Allow' | 'Deny',
    resource: string,
    context: Record<string, string> = {}
): AuthResponse => {
    const authResponse: AuthResponse = {
        principalId: principalId
    };

    if (effect && resource) {
        const policyDocument: PolicyDocument = {
            Version: '2012-10-17',
            Statement: [
                {
                    Action: 'execute-api:Invoke',
                    Effect: effect,
                    Resource: resource
                }
            ]
        };
        authResponse.policyDocument = policyDocument;
    }

    // Add context to be passed to the lambda function
    if (Object.keys(context).length > 0) {
        authResponse.context = context;
    }

    return authResponse;
};

// Main authorizer handler
export const handler = async (event: APIGatewayRequestAuthorizerEvent): Promise<AuthResponse> => {
    console.log('Authorizer event:', JSON.stringify(event, null, 2));

    try {
        // Extract token from the authorization header (REQUEST authorizer)
        const token = event.headers?.Authorization || event.headers?.authorization;

        if (!token) {
            console.log('No authorization token provided');
            throw new Error('Unauthorized');
        }

        // Remove 'Bearer ' prefix if present
        const accessToken = token.replace(/^Bearer\s+/, '');

        if (!accessToken) {
            console.log('No access token found after removing Bearer prefix');
            throw new Error('Unauthorized');
        }

        try {
            // Validate token with Cognito using AWS SDK v3
            const getUserCommand = new GetUserCommand({
                AccessToken: accessToken
            });

            const cognitoUser: GetUserCommandOutput = await cognitoClient.send(getUserCommand);
            console.log('Cognito user:', cognitoUser);

            // Extract user information from Cognito attributes
            const userAttributes: UserAttributes = {};
            if (cognitoUser.UserAttributes) {
                cognitoUser.UserAttributes.forEach((attr: AttributeType) => {
                    if (attr.Name && attr.Value) {
                        userAttributes[attr.Name] = attr.Value;
                    }
                });
            }

            // Get the DynamoDB user ID using the Cognito user ID
            const cognitoUserId = cognitoUser.Username || '';
            const dynamoUserId = await getDynamoUserIdFromCognitoId(cognitoUserId);

            // Log if DynamoDB user record is not found, but don't deny access
            if (!dynamoUserId) {
                console.warn('Could not find DynamoDB user record for Cognito user:', cognitoUserId, 'but allowing access since user is authenticated');
            }

            // Create context to pass to the lambda function
            const context: Record<string, string> = {
                userId: dynamoUserId || cognitoUserId, // Use DynamoDB user ID if available, otherwise Cognito user ID
                email: userAttributes.email || '',
                username: cognitoUser.Username || '',
                cognitoUserId: cognitoUserId,
                sub: userAttributes.sub || '',
                firstName: userAttributes.given_name || '',
                lastName: userAttributes.family_name || ''
            };

            console.log('User context:', context);

            // Generate allow policy for all resources in this API
            // Use wildcard to allow access to all methods and paths in this API
            const apiArn = event.methodArn.split('/').slice(0, 2).join('/') + '/*';
            return generatePolicy(userAttributes.sub, 'Allow', apiArn, context);

        } catch (cognitoError: any) {
            console.error('Token validation error:', cognitoError);

            // For any Cognito error (including expired tokens), return Deny
            console.log('Cognito authentication failed, returning Deny policy');
            return generatePolicy('user', 'Deny', event.methodArn);
        }

    } catch (error: any) {
        console.error('Authorizer error:', error);

        // Return deny policy for any error
        return generatePolicy('user', 'Deny', event.methodArn);
    }
};
