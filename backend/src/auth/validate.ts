import {
    CognitoIdentityProviderClient,
    GetUserCommand,
    type GetUserCommandInput
} from '@aws-sdk/client-cognito-identity-provider';
import {
    DynamoDBClient
} from '@aws-sdk/client-dynamodb';
import {
    DynamoDBDocumentClient,
    GetCommand,
    type GetCommandInput
} from '@aws-sdk/lib-dynamodb';
import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { DisplayNameProvider } from '/opt/nodejs/display-name-config';

// Types
interface UserRecord {
    id: string;
    email: string;
    username: string;
    firstName: string;
    lastName: string;
    displayName?: string;
    displayNameProvider?: DisplayNameProvider;
    cognitoUserId: string;
    createdAt: string;
    updatedAt: string;
}

// Configure AWS SDK
const awsConfig = {
    region: process.env.AWS_REGION || 'us-west-2'
};

if (process.env.AWS_SAM_LOCAL) {
    console.log('Running in SAM Local mode');
}

const cognitoClient = new CognitoIdentityProviderClient(awsConfig);
const dynamoClient = new DynamoDBClient(awsConfig);
const dynamodb = DynamoDBDocumentClient.from(dynamoClient);

// Environment variables
const USERS_TABLE = process.env.USERS_TABLE || process.env.USERSTABLE_TABLE_NAME;

// Helper function to create response
const createResponse = (statusCode: number, body: any): APIGatewayProxyResult => ({
    statusCode,
    headers: {
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token',
        'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS'
    },
    body: JSON.stringify(body)
});

// Validate token handler
export const handler = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
    console.log('ValidateToken Event:', JSON.stringify(event, null, 2));

    // Handle CORS preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return createResponse(200, {});
    }

    try {
        // Get token from Authorization header
        const authHeader = event.headers?.Authorization || event.headers?.authorization;

        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            return createResponse(401, { error: 'Authorization header with Bearer token required' });
        }

        const accessToken = authHeader.substring(7);

        try {
            // Validate token with Cognito
            const getUserParams: GetUserCommandInput = {
                AccessToken: accessToken
            };

            const getUserCommand = new GetUserCommand(getUserParams);
            const cognitoUser = await cognitoClient.send(getUserCommand);

            // Get the user ID from Cognito (this is the 'sub' claim in the JWT)
            const userId = cognitoUser.Username; // This is actually the user ID in Cognito

            if (!userId) {
                return createResponse(401, { error: 'Invalid token - no user ID' });
            }

            if (!USERS_TABLE) {
                return createResponse(500, { error: 'Users table configuration missing' });
            }

            // Get user details from DynamoDB using user ID
            const getCommand = new GetCommand({
                TableName: USERS_TABLE,
                Key: {
                    id: userId
                }
            });
            const userResult = await dynamodb.send(getCommand);

            if (!userResult.Item) {
                return createResponse(404, { error: 'User not found' });
            }

            const user = userResult.Item as UserRecord;

            return createResponse(200, {
                message: 'Token is valid',
                valid: true,
                user: {
                    id: user.id,
                    email: user.email,
                    username: user.username,
                    firstName: user.firstName,
                    lastName: user.lastName
                }
            });

        } catch (cognitoError: any) {
            console.error('Token validation error:', cognitoError);
            return createResponse(401, {
                error: 'Invalid or expired token',
                valid: false
            });
        }

    } catch (error: any) {
        console.error('Validate token error:', error);
        return createResponse(500, { error: 'Internal server error', details: error.message });
    }
};
