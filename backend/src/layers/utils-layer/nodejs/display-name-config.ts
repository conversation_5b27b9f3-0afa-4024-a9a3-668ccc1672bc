/**
 * Display Name Configuration
 * 
 * Centralized configuration for display name providers and related constants.
 * This file ensures consistency across all backend services and avoids
 * hardcoding provider lists in multiple places.
 */

// Available display name providers
export const DISPLAY_NAME_PROVIDERS = [
    'gf',
    'xbox',
    'playstation',
    'steam',
    'discord',
    'twitch'
] as const;

// Type for display name providers
export type DisplayNameProvider = typeof DISPLAY_NAME_PROVIDERS[number];

// Providers that support account linking (excludes 'gf' which is native)
export const LINKABLE_PROVIDERS: readonly DisplayNameProvider[] = [
    'xbox',
    'playstation',
    'steam',
    'discord',
    'twitch'
] as const;

// Default provider for new accounts
export const DEFAULT_PROVIDER: DisplayNameProvider = 'gf';

// Display name validation constants
export const DISPLAY_NAME_CONFIG = {
    // Maximum length for display names
    MAX_LENGTH: 32,

    // Minimum length for display names  
    MIN_LENGTH: 3,

    // Regex pattern for valid display names
    PATTERN: /^[a-zA-Z0-9_-]+$/,

    // Default provider
    DEFAULT_PROVIDER,

    // All available providers
    ALL_PROVIDERS: DISPLAY_NAME_PROVIDERS,

    // Linkable providers only
    LINKABLE_PROVIDERS
} as const;

/**
 * Check if a provider is valid
 */
export function isValidProvider(provider: string): provider is DisplayNameProvider {
    return DISPLAY_NAME_PROVIDERS.includes(provider as DisplayNameProvider);
}

/**
 * Check if a provider supports linking
 */
export function supportsLinking(provider: string): boolean {
    return LINKABLE_PROVIDERS.includes(provider as DisplayNameProvider);
}

/**
 * Get provider display name for UI purposes
 */
export function getProviderDisplayName(provider: DisplayNameProvider): string {
    switch (provider) {
        case 'gf':
            return 'GameFlex';
        case 'xbox':
            return 'Xbox Live';
        case 'playstation':
            return 'PlayStation';
        case 'steam':
            return 'Steam';
        case 'discord':
            return 'Discord';
        case 'twitch':
            return 'Twitch';
        default:
            // This should never happen due to the type constraint, but provide a fallback
            return (provider as string).toUpperCase();
    }
}

/**
 * Format display name with provider indicator
 */
export function formatDisplayName(displayName: string, provider?: DisplayNameProvider): string {
    if (!provider || provider === DEFAULT_PROVIDER) {
        return displayName;
    }
    return `[${provider}]${displayName}`;
}

/**
 * Validate display name format
 */
export function isValidDisplayName(displayName: string): boolean {
    if (!displayName || displayName.length < DISPLAY_NAME_CONFIG.MIN_LENGTH) {
        return false;
    }

    if (displayName.length > DISPLAY_NAME_CONFIG.MAX_LENGTH) {
        return false;
    }

    return DISPLAY_NAME_CONFIG.PATTERN.test(displayName);
}

/**
 * Interface for display name option
 */
export interface DisplayNameOption {
    provider: DisplayNameProvider;
    displayName: string;
    formattedDisplayName: string;
    isAvailable: boolean;
}

/**
 * Interface for linked account
 */
export interface LinkedAccount {
    type: Exclude<DisplayNameProvider, 'gf'>; // All providers except 'gf'
    accountId: string;
    displayName: string;
    profilePictureUrl?: string;
    linkedAt: string;
}
