# Outputs for Database Module

# Table objects for use by other modules
output "tables" {
  description = "Map of all DynamoDB tables"
  value = {
    posts           = aws_dynamodb_table.posts
    media           = aws_dynamodb_table.media
    user_profiles   = aws_dynamodb_table.user_profiles
    users           = aws_dynamodb_table.users
    likes           = aws_dynamodb_table.likes
    follows         = aws_dynamodb_table.follows
    channels        = aws_dynamodb_table.channels
    channel_members = aws_dynamodb_table.channel_members
    reflexes        = aws_dynamodb_table.reflexes
    reflex_likes    = aws_dynamodb_table.reflex_likes
    reflex_reactions = aws_dynamodb_table.reflex_reactions
    comments        = aws_dynamodb_table.comments
    xbox_accounts   = aws_dynamodb_table.xbox_accounts
    post_views      = aws_dynamodb_table.post_views
    post_reactions  = aws_dynamodb_table.post_reactions
    post_engagement_metrics = aws_dynamodb_table.post_engagement_metrics
    user_preferences = aws_dynamodb_table.user_preferences
    user_history    = aws_dynamodb_table.user_history
  }
}

# Table names
output "table_names" {
  description = "Map of table names"
  value = {
    posts           = aws_dynamodb_table.posts.name
    media           = aws_dynamodb_table.media.name
    user_profiles   = aws_dynamodb_table.user_profiles.name
    users           = aws_dynamodb_table.users.name
    likes           = aws_dynamodb_table.likes.name
    follows         = aws_dynamodb_table.follows.name
    channels        = aws_dynamodb_table.channels.name
    channel_members = aws_dynamodb_table.channel_members.name
    reflexes        = aws_dynamodb_table.reflexes.name
    reflex_likes    = aws_dynamodb_table.reflex_likes.name
    reflex_reactions = aws_dynamodb_table.reflex_reactions.name
    comments        = aws_dynamodb_table.comments.name
    xbox_accounts   = aws_dynamodb_table.xbox_accounts.name
    post_views      = aws_dynamodb_table.post_views.name
    post_reactions  = aws_dynamodb_table.post_reactions.name
    post_engagement_metrics = aws_dynamodb_table.post_engagement_metrics.name
    user_preferences = aws_dynamodb_table.user_preferences.name
    user_history    = aws_dynamodb_table.user_history.name
  }
}

# Table ARNs
output "table_arns" {
  description = "Map of table ARNs"
  value = {
    posts           = aws_dynamodb_table.posts.arn
    media           = aws_dynamodb_table.media.arn
    user_profiles   = aws_dynamodb_table.user_profiles.arn
    users           = aws_dynamodb_table.users.arn
    likes           = aws_dynamodb_table.likes.arn
    follows         = aws_dynamodb_table.follows.arn
    channels        = aws_dynamodb_table.channels.arn
    channel_members = aws_dynamodb_table.channel_members.arn
    reflexes        = aws_dynamodb_table.reflexes.arn
    reflex_likes    = aws_dynamodb_table.reflex_likes.arn
    reflex_reactions = aws_dynamodb_table.reflex_reactions.arn
    comments        = aws_dynamodb_table.comments.arn
    xbox_accounts   = aws_dynamodb_table.xbox_accounts.arn
    post_views      = aws_dynamodb_table.post_views.arn
    post_reactions  = aws_dynamodb_table.post_reactions.arn
    post_engagement_metrics = aws_dynamodb_table.post_engagement_metrics.arn
    user_preferences = aws_dynamodb_table.user_preferences.arn
    user_history    = aws_dynamodb_table.user_history.arn
  }
}
